from abc import ABC, abstractmethod
from dataclasses import dataclass
from decimal import Decimal

from .account import Position, PositionHistory


@dataclass
class PositionSignal:
    side: str
    quantity: Decimal


class TradingStrategy(ABC):
    """Classe base para estratégias de trading"""

    price_history: list[Decimal]

    @abstractmethod
    def on_market_refresh(
        self,
        market_price: Decimal,
        current_position: Position | None,
        positionHistory: list[PositionHistory],
        balance: Decimal
    ) -> PositionSignal | None:
        pass


class SimpleMovingAverageStrategy(TradingStrategy):
    """Estratégia baseada em média móvel simples"""

    def __init__(self, short_period: int = 10, long_period: int = 30):
        self.short_period = short_period
        self.long_period = long_period
        self.price_history = []

    def _calculate_sma(self, period: int) -> Decimal:
        """Calcula média móvel simples"""
        if len(self.price_history) < period:
            return Decimal("0")
        return sum(self.price_history[-period:]) / Decimal(str(period))

    def should_buy(self, market_price: Decimal) -> bool:
        """Compra quando SMA curta cruza acima da SMA longa"""
        if len(self.price_history) < self.long_period:
            return False

        short_sma = self._calculate_sma(self.short_period)
        long_sma = self._calculate_sma(self.long_period)

        return short_sma > long_sma

    def should_sell(self, market_price: Decimal, position: Position) -> bool:
        """Vende quando SMA curta cruza abaixo da SMA longa"""
        if len(self.price_history) < self.long_period:
            return False

        short_sma = self._calculate_sma(self.short_period)
        long_sma = self._calculate_sma(self.long_period)

        return short_sma < long_sma

    def calculate_quantity(self, balance: Decimal, price: Decimal) -> Decimal:
        """Calcula quantidade baseada em 10% do saldo"""
        quantity = (balance * Decimal("0.1")) / price
        return quantity

    def on_market_refresh(
        self,
        market_price: Decimal,
        current_position: Position | None,
        positionHistory: list[PositionHistory],
        balance: Decimal
    ) -> PositionSignal | None:
        self.price_history.append(market_price)
        if len(self.price_history) > self.long_period:
            self.price_history.pop(0)

        if not current_position:
            if self.should_buy(market_price):
                return PositionSignal(
                    "buy", self.calculate_quantity(balance, market_price)
                )
        else:
            if self.should_sell(market_price, current_position):
                return PositionSignal("sell", current_position.quantity)
        return None


class IterationStrategy(TradingStrategy):
    """
    Estratégia baseada em número de iterações.

    Compra na primeira oportunidade e vende após um número específico de iterações.
    Utiliza 80% do saldo disponível para cada operação de compra.

    Args:
        sell_on_iteration (int): Número de iterações para vender
    """

    def __init__(
        self,
        sell_on_iteration=5,
    ):
        self.sell_on_iteration = int(sell_on_iteration)
        self.price_history = []

    def calculate_quantity(self, balance: Decimal, price: Decimal) -> Decimal:
        quantity = (balance * Decimal("0.8")) / price
        return quantity

    def on_market_refresh(
        self,
        market_price: Decimal,
        current_position: Position | None,
        positionHistory: list[PositionHistory],
        balance: Decimal
    ) -> PositionSignal | None:
        self.price_history.append(market_price)
        if not current_position:
            return PositionSignal(
                "buy", self.calculate_quantity(balance, market_price)
            )
        else:
            if len(self.price_history) > self.sell_on_iteration:
                self.price_history = []
                return PositionSignal("sell", current_position.quantity)
        return None


class PercentageStrategy(TradingStrategy):
    """
    Estratégia baseada em percentuais.

    Compra quando o preço cai X% em relação ao preço de referência.
    Vende quando o preço atinge um percentual de stop_loss calculado
    em cima do valor máximo atingido por aquela posição, ou quando
    atinge o percentual de ganho especificado.

    Args:
        buy_drop_percentage (float): Percentual de queda para comprar (ex: 2.0 para 2%)
        stop_loss_percentage (float): Percentual de stop loss (ex: 5.0 para 5%)
        gain_percentage (float): Percentual de ganho para vender (ex: 3.0 para 3%)
        position_percentage (float): Percentual do saldo a usar (ex: 0.8 para 80%)
    """

    def __init__(
        self,
        buy_drop_percentage: float,
        stop_loss_percentage: float,
        gain_percentage: float,
        position_percentage: float,
    ):
        self.buy_drop_percentage = Decimal(str(buy_drop_percentage))
        self.stop_loss_percentage = Decimal(str(stop_loss_percentage))
        self.gain_percentage = Decimal(str(gain_percentage))
        self.position_percentage = Decimal(str(position_percentage))
        self.price_history = []
        self.reference_price: Decimal | None = None
        self.position_max_price: Decimal | None = None
        self.position_buy_price: Decimal | None = None

    def calculate_quantity(self, balance: Decimal, price: Decimal) -> Decimal:
        """Calcula quantidade baseada no percentual configurado do saldo"""
        quantity = (balance * self.position_percentage) / price
        return quantity

    def should_buy(self, market_price: Decimal) -> bool:
        """
        Compra quando o preço cai X% em relação ao preço de referência.
        Se não há preço de referência, usa o primeiro preço como referência.
        """
        if self.reference_price is None:
            self.reference_price = market_price
            return False

        # Calcula o percentual de queda
        price_drop_percentage = ((self.reference_price - market_price) / self.reference_price) * Decimal("100")

        # Compra se a queda for maior ou igual ao percentual configurado
        if price_drop_percentage >= self.buy_drop_percentage:
            # Atualiza o preço de referência para o preço atual após a compra
            self.reference_price = market_price
            return True

        # Atualiza o preço de referência se o preço atual for maior
        if market_price > self.reference_price:
            self.reference_price = market_price

        return False

    def should_sell(self, market_price: Decimal) -> bool:
        """
        Vende quando o preço atinge um percentual de stop_loss
        calculado em cima do valor máximo atingido por aquela posição,
        ou quando atinge o percentual de ganho especificado.
        """
        # Atualiza o preço máximo da posição
        if self.position_max_price is None or market_price > self.position_max_price:
            self.position_max_price = market_price

        # Verifica se deve vender por ganho percentual
        if self.position_buy_price is not None:
            gain_percentage = ((market_price - self.position_buy_price) / self.position_buy_price) * Decimal("100")
            if gain_percentage >= self.gain_percentage:
                return True

        # Calcula o preço de stop loss baseado no máximo atingido
        stop_loss_price = self.position_max_price * (Decimal("100") - self.stop_loss_percentage) / Decimal("100")

        # Vende se o preço atual for menor ou igual ao stop loss
        return market_price <= stop_loss_price

    def on_market_refresh(
        self,
        market_price: Decimal,
        current_position: Position | None,
        positionHistory: list[PositionHistory],
        balance: Decimal
    ) -> PositionSignal | None:
        """Processa atualização do mercado e retorna sinal de trading"""
        self.price_history.append(market_price)

        # Mantém apenas os últimos 100 preços para não consumir muita memória
        if len(self.price_history) > 100:
            self.price_history.pop(0)

        if not current_position:
            # Reset dos valores quando não há posição
            self.position_max_price = None
            self.position_buy_price = None

            # Verifica se deve comprar
            if self.should_buy(market_price):
                # Registra o preço de compra
                self.position_buy_price = market_price
                return PositionSignal(
                    "buy", self.calculate_quantity(balance, market_price)
                )
        else:
            # Verifica se deve vender
            if self.should_sell(market_price):
                # Reset dos valores após venda
                self.position_max_price = None
                self.position_buy_price = None
                return PositionSignal("sell", current_position.quantity)

        return None
